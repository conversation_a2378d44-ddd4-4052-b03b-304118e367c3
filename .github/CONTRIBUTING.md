# Contributing

Thanks for your interest in contributing to Tailwind CSS IntelliSense! Please take a moment to review this document **before submitting a pull request**.

## Pull requests

**Please ask first before starting work on any significant new features.**

It's never a fun experience to have your pull request declined after investing a lot of time and effort into a new feature. To avoid this from happening, we request that contributors create [a feature request](https://github.com/tailwindlabs/tailwindcss/discussions/new?category=ideas) to first discuss any significant new ideas.

## Building the Extension

You can build the VSIX package by running these commands in the project root.

```bash
pnpm install
pnpx run package --workspace=packages/vscode-tailwindcss
```
