---
name: Bug report
about: If you think something is broken with Tailwind CSS IntelliSense itself, create a bug report.
title: ''
labels: ''
assignees: ''
---

**What version of VS Code are you using?**

For example: v1.78.2

**What version of Tailwind CSS IntelliSense are you using?**

For example: v0.7.0

**What version of Tailwind CSS are you using?**

For example: v2.0.4

**What package manager are you using?**

For example: npm, yarn

**What operating system are you using?**

For example: macOS, Windows

**Tailwind CSS Stylesheet (v4) or config file (v3)**

```js
// Paste the contents of your CSS file or config file here
```

**VS Code settings**

```json
// Paste your VS Code settings in JSON format here
```

**Reproduction URL**

A public GitHub repo that includes a minimal reproduction of the bug. **Please do not link to your actual project**, what we need instead is a _minimal_ reproduction in a fresh project without any unnecessary code. This means it doesn't matter if your real project is private/confidential, since we want a link to a separate, isolated reproduction anyways.

**Describe your issue**

Describe the problem you're seeing, any important steps to reproduce and what behavior you expect instead
