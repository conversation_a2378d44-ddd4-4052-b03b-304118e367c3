name: Run Tests
on:
  pull_request:
    branches:
      - main

jobs:
  tests:
    strategy:
      fail-fast: false
      matrix:
        node: [18, 20, 22, 24]
        os:
          - namespace-profile-default
          - namespace-profile-macos-arm64
          - namespace-profile-windows-amd64

    runs-on: ${{ matrix.os }}
    name: Run Tests - Node v${{ matrix.node }} / ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          cache: 'pnpm'
          node-version: ${{ matrix.node }}

      - name: Install dependencies
        run: pnpm install

      - name: Run syntax tests
        working-directory: packages/tailwindcss-language-syntax
        run: pnpm run build && pnpm run test

      - name: Run service tests
        working-directory: packages/tailwindcss-language-service
        run: pnpm run build && pnpm run test

      - name: Run server tests
        working-directory: packages/tailwindcss-language-server
        run: pnpm run build && pnpm run test
