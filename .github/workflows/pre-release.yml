name: Publish pre-release
concurrency: publish
on:
  push:
    branches: [main]
jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v3
        with:
          version: ^9.6.0
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Build everything
        run: pnpm -r run build
      - name: Run tests
        run: pnpm --filter ./packages/tailwindcss-language-server run test
      - name: Bump IntelliSense version
        run: >
          node .github/workflows/bump-version.mjs &&
          cat packages/vscode-tailwindcss/package.json
      - name: Resolve LSP version
        run: |
          echo "SHA_SHORT=$(git rev-parse --short HEAD)" >> $GITHUB_ENV
      - name: 'Version LSP based on commit: 0.0.0-insiders.${{ env.SHA_SHORT }}'
        run: >
          cd packages/tailwindcss-language-server &&
          pnpm version 0.0.0-insiders.${{ env.SHA_SHORT }} --force --no-git-tag-version
      - name: Publish IntelliSense
        env:
          VSCODE_TOKEN: ${{ secrets.VSCODE_TOKEN }}
        run: >
          cd packages/vscode-tailwindcss &&
          pnpm run publish --pre-release -p $VSCODE_TOKEN
      - name: Publish LSP
        run: >
          cd packages/tailwindcss-language-server &&
          pnpm publish --tag insiders --access public --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
