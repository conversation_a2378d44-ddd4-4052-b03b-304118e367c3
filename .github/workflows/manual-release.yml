name: Publish Release
concurrency: publish
on:
  workflow_dispatch: {}

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v3
        with:
          version: ^9.6.0
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          registry-url: 'https://registry.npmjs.org'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Run tests
        run: >
          cd packages/tailwindcss-language-server &&
          pnpm run build &&
          pnpm run test
      - name: Publish IntelliSense
        env:
          VSCODE_TOKEN: ${{ secrets.VSCODE_TOKEN }}
        run: >
          cd packages/vscode-tailwindcss &&
          pnpm run publish -p $VSCODE_TOKEN
