{"name": "@tailwindcss/language-server", "version": "0.14.23", "description": "Tailwind CSS Language Server", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tailwindlabs/tailwindcss-intellisense.git", "directory": "packages/tailwindcss-language-server"}, "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss-intellisense/issues"}, "homepage": "https://github.com/tailwindlabs/tailwindcss-intellisense/tree/HEAD/packages/tailwindcss-language-server#readme", "scripts": {"build": "pnpm run clean && pnpm run _esbuild && pnpm run _esbuild:css && pnpm run hashbang", "_esbuild": "node ../../esbuild.mjs src/server.ts --outfile=bin/tailwindcss-language-server --minify", "_esbuild:css": "node ../../esbuild.mjs src/language/css.ts --outfile=bin/css-language-server --minify", "clean": "<PERSON><PERSON><PERSON> bin", "hashbang": "node scripts/hashbang.mjs", "create-notices-file": "node scripts/createNoticesFile.mjs", "prepublishOnly": "pnpm run build", "test": "vitest", "pretest": "node tests/prepare.mjs"}, "bin": {"tailwindcss-language-server": "./bin/tailwindcss-language-server"}, "files": ["bin", "ThirdPartyNotices.txt"], "publishConfig": {"access": "public"}, "devDependencies": {"@parcel/watcher": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/container-queries": "0.1.0", "@tailwindcss/forms": "0.5.3", "@tailwindcss/language-service": "workspace:*", "@tailwindcss/line-clamp": "0.4.2", "@tailwindcss/oxide": "^4.1.0", "@tailwindcss/typography": "0.5.7", "@types/braces": "3.0.1", "@types/color-name": "^1.1.3", "@types/culori": "^2.1.0", "@types/debounce": "1.2.0", "@types/dlv": "^1.1.4", "@types/find-up": "^4.0.0", "@types/license-checker": "^25.0.6", "@types/node": "^18.19.33", "@types/normalize-path": "^3.0.2", "@types/picomatch": "^2.3.3", "@types/postcss-import": "^14.0.3", "@types/stack-trace": "^0.0.33", "@types/vscode": "1.65.0", "browserslist": "^4.23.0", "bun-types": "^1.1.1", "chokidar": "3.6.0", "color-name": "1.1.4", "culori": "^4.0.1", "debounce": "1.2.0", "dedent": "^1.5.3", "deepmerge": "4.2.2", "dlv": "1.1.3", "dset": "3.1.4", "enhanced-resolve": "^5.16.1", "esbuild": "^0.25.5", "find-up": "5.0.0", "jiti": "^2.3.3", "klona": "2.0.4", "license-checker": "25.0.1", "minimist": "^1.2.8", "normalize-path": "3.0.0", "picomatch": "^4.0.1", "pkg-up": "3.1.0", "postcss": "8.5.4", "postcss-import": "^16.1.0", "postcss-load-config": "3.0.1", "postcss-selector-parser": "6.0.2", "resolve": "1.20.0", "rimraf": "3.0.2", "stack-trace": "0.0.10", "tailwindcss": "3.4.17", "tailwindcss-v4": "npm:tailwindcss@4.1.1", "tinyglobby": "^0.2.12", "tsconfck": "^3.1.4", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.1", "vscode-css-languageservice": "6.3.6", "vscode-jsonrpc": "8.2.0", "vscode-languageclient": "8.1.0", "vscode-languageserver": "8.1.0", "vscode-languageserver-protocol": "^3.17.5", "vscode-languageserver-textdocument": "1.0.12", "vscode-uri": "3.0.2"}, "engines": {"node": ">=18.0.0"}}