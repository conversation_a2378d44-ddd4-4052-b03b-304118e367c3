# Tailwind CSS Language Server

[Language Server Protocol](https://github.com/Microsoft/language-server-protocol) implementation for [Tailwind CSS](https://tailwindcss.com), used by [Tailwind CSS IntelliSense for VS Code](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss).

## Install

```bash
npm install -g @tailwindcss/language-server
```

## Run

```bash
tailwindcss-language-server --stdio
```

```
Usage: tailwindcss-language-server [options]

Options:

  --stdio          use stdio
  --node-ipc       use node-ipc
  --socket=<port>  use socket
```
