{"code": "<div className={'lowercase uppercase' + 'uppercase'}>", "language": "javascriptreact", "expected": [{"code": "cssConflict", "className": {"className": "lowercase", "classList": {"classList": "lowercase uppercase", "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 36}}}, "relativeRange": {"start": {"line": 0, "character": 0}, "end": {"line": 0, "character": 9}}, "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 26}}}, "otherClassNames": [{"className": "uppercase", "classList": {"classList": "lowercase uppercase", "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 36}}}, "relativeRange": {"start": {"line": 0, "character": 10}, "end": {"line": 0, "character": 19}}, "range": {"start": {"line": 0, "character": 27}, "end": {"line": 0, "character": 36}}}], "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 26}}, "severity": 2, "message": "'lowercase' applies the same CSS properties as 'uppercase'.", "relatedInformation": [{"message": "uppercase", "location": {"uri": "{{URI}}", "range": {"start": {"line": 0, "character": 27}, "end": {"line": 0, "character": 36}}}}]}, {"code": "cssConflict", "className": {"className": "uppercase", "classList": {"classList": "lowercase uppercase", "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 36}}}, "relativeRange": {"start": {"line": 0, "character": 10}, "end": {"line": 0, "character": 19}}, "range": {"start": {"line": 0, "character": 27}, "end": {"line": 0, "character": 36}}}, "otherClassNames": [{"className": "lowercase", "classList": {"classList": "lowercase uppercase", "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 36}}}, "relativeRange": {"start": {"line": 0, "character": 0}, "end": {"line": 0, "character": 9}}, "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 26}}}], "range": {"start": {"line": 0, "character": 27}, "end": {"line": 0, "character": 36}}, "severity": 2, "message": "'uppercase' applies the same CSS properties as 'lowercase'.", "relatedInformation": [{"message": "lowercase", "location": {"uri": "{{URI}}", "range": {"start": {"line": 0, "character": 17}, "end": {"line": 0, "character": 26}}}}]}]}