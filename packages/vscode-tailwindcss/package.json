{"name": "vscode-tailwindcss", "version": "0.14.23", "displayName": "Tailwind CSS IntelliSense", "description": "Intelligent Tailwind CSS tooling for VS Code", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/tailwindlabs/tailwindcss-intellisense", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss-intellisense/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/tailwindlabs/tailwindcss-intellisense.git"}, "publisher": "bradlc", "keywords": ["tailwind", "tailwindcss", "css", "intellisense", "autocomplete", "vscode"], "engines": {"vscode": "^1.67.0"}, "categories": ["Linters", "Other"], "galleryBanner": {"color": "#f9fafb"}, "icon": "media/icon.png", "activationEvents": ["onStartupFinished"], "main": "dist/extension.js", "capabilities": {"virtualWorkspaces": false}, "contributes": {"languages": [{"id": "tailwindcss", "aliases": ["Tailwind CSS"], "configuration": "./tailwindcss.language.configuration.json", "icon": {"light": "media/icon.png", "dark": "media/icon.png"}}], "commands": [{"command": "tailwindCSS.showOutput", "title": "Tailwind CSS: Show Output", "enablement": "tailwindCSS.hasOutputChannel"}, {"command": "tailwindCSS.sortSelection", "title": "Tailwind CSS: Sort Selection", "enablement": "editorHasSelection && (resourceScheme == file || resourceScheme == vscode-remote) && tailwindCSS.activeTextEditorSupportsClassSorting"}], "grammars": [{"language": "tailwindcss", "scopeName": "source.css.tailwind", "path": "./syntaxes/source.css.tailwind.tmLanguage.json", "tokenTypes": {"meta.function.url string.quoted": "other"}}, {"scopeName": "tailwindcss.at-rules.injection", "path": "./syntaxes/at-rules.tmLanguage.json", "injectTo": ["source.css", "source.vue", "source.svelte", "text.html", "source.gjs", "source.gts"]}, {"scopeName": "tailwindcss.at-rules.scss.injection", "path": "./syntaxes/at-rules.scss.tmLanguage.json", "injectTo": ["source.css.scss"]}, {"scopeName": "tailwindcss.at-rules.postcss.injection", "path": "./syntaxes/at-rules.postcss.tmLanguage.json", "injectTo": ["source.css.postcss"]}, {"scopeName": "tailwindcss.at-apply.injection", "path": "./syntaxes/at-apply.tmLanguage.json", "injectTo": ["source.css", "source.css.postcss", "source.vue", "source.svelte", "text.html", "source.gjs", "source.gts"]}, {"scopeName": "tailwindcss.theme-fn.injection", "path": "./syntaxes/theme-fn.tmLanguage.json", "injectTo": ["source.css", "source.css.postcss", "source.vue", "source.svelte", "text.html", "source.gjs", "source.gts"]}, {"scopeName": "tailwindcss.screen-fn.injection", "path": "./syntaxes/screen-fn.tmLanguage.json", "injectTo": ["source.css", "source.css.postcss", "source.vue", "source.svelte", "text.html", "source.gjs", "source.gts"]}], "configuration": {"title": "Tailwind CSS IntelliSense", "properties": {"tailwindCSS.emmetCompletions": {"type": "boolean", "default": false, "markdownDescription": "Enable class name completions when using Emmet-style syntax, for example `div.bg-red-500.uppercase`"}, "tailwindCSS.includeLanguages": {"type": "object", "additionalProperties": {"type": "string"}, "default": {}, "markdownDescription": "Enable features in languages that are not supported by default. Add a mapping here between the new language and an already supported language.\n E.g.: `{\"plaintext\": \"html\"}`"}, "tailwindCSS.files.exclude": {"type": "array", "items": {"type": "string"}, "default": ["**/.git/**", "**/node_modules/**", "**/.hg/**", "**/.svn/**"], "markdownDescription": "Configure glob patterns to exclude from all IntelliSense features. Inherits all glob patterns from the `#files.exclude#` setting."}, "tailwindCSS.classAttributes": {"type": "array", "items": {"type": "string"}, "default": ["class", "className", "ngClass", "class:list"], "markdownDescription": "The HTML attributes for which to provide class completions, hover previews, linting etc."}, "tailwindCSS.classFunctions": {"type": "array", "items": {"type": "string"}, "default": [], "markdownDescription": "The function or tagged template literal names for which to provide class completions, hover previews, linting etc."}, "tailwindCSS.suggestions": {"type": "boolean", "default": true, "markdownDescription": "Enable autocomplete suggestions.", "scope": "language-overridable"}, "tailwindCSS.hovers": {"type": "boolean", "default": true, "markdownDescription": "Enable hovers.", "scope": "language-overridable"}, "tailwindCSS.codeActions": {"type": "boolean", "default": true, "markdownDescription": "Enable code actions.", "scope": "language-overridable"}, "tailwindCSS.codeLens": {"type": "boolean", "default": true, "markdownDescription": "Enable code lens.", "scope": "language-overridable"}, "tailwindCSS.colorDecorators": {"type": "boolean", "default": true, "markdownDescription": "Controls whether the editor should render inline color decorators for Tailwind CSS classes and helper functions.", "scope": "language-overridable"}, "tailwindCSS.validate": {"type": "boolean", "default": true, "markdownDescription": "Enable linting. Rules can be configured individually using the `tailwindcss.lint.*` settings", "scope": "language-overridable"}, "tailwindCSS.lint.cssConflict": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "Class names on the same HTML element which apply the same CSS property or properties", "scope": "language-overridable"}, "tailwindCSS.lint.invalidApply": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "error", "markdownDescription": "Unsupported use of the [`@apply` directive](https://tailwindcss.com/docs/functions-and-directives/#apply)", "scope": "language-overridable"}, "tailwindCSS.lint.invalidScreen": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "error", "markdownDescription": "Unknown screen name used with the [`@screen` directive](https://tailwindcss.com/docs/functions-and-directives/#screen)", "scope": "language-overridable"}, "tailwindCSS.lint.invalidVariant": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "error", "markdownDescription": "Unknown variant name used with the [`@variants` directive](https://tailwindcss.com/docs/functions-and-directives/#variants)", "scope": "language-overridable"}, "tailwindCSS.lint.invalidConfigPath": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "error", "markdownDescription": "Unknown or invalid path used with the [`theme` helper](https://tailwindcss.com/docs/functions-and-directives/#theme)", "scope": "language-overridable"}, "tailwindCSS.lint.invalidTailwindDirective": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "error", "markdownDescription": "Unknown value used with the [`@tailwind` directive](https://tailwindcss.com/docs/functions-and-directives/#tailwind)", "scope": "language-overridable"}, "tailwindCSS.lint.recommendedVariantOrder": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "Class variants not in the recommended order (applies in [JIT mode](https://tailwindcss.com/docs/just-in-time-mode) only)", "scope": "language-overridable"}, "tailwindCSS.lint.usedBlocklistedClass": {"type": "string", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "Usage of class names that have been blocklisted via `@source not inline(…)`", "scope": "language-overridable"}, "tailwindCSS.experimental.classRegex": {"type": "array", "scope": "language-overridable"}, "tailwindCSS.experimental.configFile": {"type": ["null", "string", "object"], "default": null, "markdownDescription": "Manually specify the Tailwind config file or files that should be read to provide IntelliSense features. Can either be a single string value, or an object where each key is a config file path and each value is a glob or array of globs representing the set of files that the config file applies to."}, "tailwindCSS.showPixelEquivalents": {"type": "boolean", "default": true, "markdownDescription": "Show `px` equivalents for `rem` CSS values."}, "tailwindCSS.rootFontSize": {"type": "number", "default": 16, "markdownDescription": "Root font size in pixels. Used to convert `rem` CSS values to their `px` equivalents. See `#tailwindCSS.showPixelEquivalents#`."}, "tailwindCSS.inspectPort": {"type": ["number", "null"], "default": null, "markdownDescription": "Enable the Node.js inspector agent for the language server and listen on the specified port."}, "tailwindcss-intellisense.trace.server": {"scope": "window", "type": "string", "enum": ["off", "messages", "verbose"], "default": "off", "description": "Traces the communication between VS Code and the Tailwind CSS Language Server."}}}}, "scripts": {"_esbuild": "node ../../esbuild.mjs src/extension.ts src/server.ts src/cssServer.ts --outdir=dist", "dev": "concurrently --raw --kill-others \"pnpm run watch\" \"pnpm run check --watch\"", "watch": "pnpm run clean && pnpm run _esbuild --watch", "build": "pnpm run check && pnpm run clean && pnpm run _esbuild --minify && move-file dist/server.js dist/tailwindServer.js && move-file dist/cssServer.js dist/tailwindModeServer.js", "package": "vsce package --no-dependencies", "publish": "vsce publish --no-dependencies", "copy:notices": "cp ../tailwindcss-language-server/ThirdPartyNotices.txt ./dist/ThirdPartyNotices.txt", "vscode:prepublish": "pnpm run build && pnpm run copy:notices", "clean": "<PERSON><PERSON><PERSON> dist", "check": "tsc --noEmit"}, "devDependencies": {"@tailwindcss/language-server": "workspace:*", "@tailwindcss/language-service": "workspace:*", "@types/braces": "3.0.1", "@types/picomatch": "^2.3.3", "@types/vscode": "1.65.0", "@vscode/vsce": "2.21.1", "braces": "3.0.3", "color-name": "1.1.4", "concurrently": "9.1.2", "esbuild": "^0.25.5", "minimist": "^1.2.8", "move-file-cli": "3.0.0", "normalize-path": "3.0.0", "picomatch": "^4.0.1", "rimraf": "3.0.2", "typescript": "5.8.3", "vscode-languageclient": "8.0.2"}}