{"name": "@tailwindcss/language-service", "version": "0.14.23", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist"], "scripts": {"start": "node ./scripts/build.mjs --watch", "build": "node ./scripts/build.mjs", "prepublishOnly": "pnpm run build", "test": "vitest"}, "dependencies": {"@csstools/css-calc": "2.1.2", "@csstools/css-parser-algorithms": "3.0.4", "@csstools/css-tokenizer": "3.0.3", "@csstools/media-query-list-parser": "2.0.4", "@types/culori": "^2.1.0", "@types/moo": "0.5.3", "@types/semver": "7.3.10", "braces": "3.0.3", "color-name": "1.1.4", "css.escape": "1.5.1", "culori": "^4.0.1", "detect-indent": "6.0.0", "dlv": "1.1.3", "dset": "3.1.4", "line-column": "1.0.2", "moo": "0.5.1", "postcss": "8.5.4", "postcss-selector-parser": "6.0.2", "postcss-value-parser": "4.2.0", "semver": "7.7.1", "sift-string": "0.0.2", "stringify-object": "3.3.0", "tmp-cache": "1.1.0", "vscode-emmet-helper-bundled": "0.0.1", "vscode-languageserver": "8.1.0", "vscode-languageserver-textdocument": "1.0.12"}, "devDependencies": {"@types/braces": "3.0.1", "@types/css.escape": "^1.5.2", "@types/dedent": "^0.7.2", "@types/line-column": "^1.0.2", "@types/node": "^18.19.33", "@types/stringify-object": "^4.0.5", "dedent": "^1.5.3", "esbuild": "^0.25.5", "esbuild-node-externals": "^1.9.0", "minimist": "^1.2.8", "tslib": "2.2.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.1"}}